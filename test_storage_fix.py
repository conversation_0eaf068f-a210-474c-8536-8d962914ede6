#!/usr/bin/env python
"""
Script para testar a correção do problema de storage
"""
import os
import sys
import django

# Configurar Django
sys.path.append('kontent')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.core.files.storage import default_storage
from config import settings

def test_storage_configuration():
    """Testa se a configuração de storage está correta"""
    print("=== Teste de Configuração de Storage ===")
    
    # Verificar configurações
    print(f"BASE_DIR: {settings.BASE_DIR}")
    print(f"MEDIA_ROOT: {settings.MEDIA_ROOT}")
    print(f"TEMP_UPLOAD_FOLDER: {settings.TEMP_UPLOAD_FOLDER}")
    
    # Verificar se os caminhos são iguais
    media_root_abs = os.path.abspath(settings.MEDIA_ROOT)
    temp_folder_abs = os.path.abspath(settings.TEMP_UPLOAD_FOLDER)
    
    print(f"MEDIA_ROOT (absoluto): {media_root_abs}")
    print(f"TEMP_UPLOAD_FOLDER (absoluto): {temp_folder_abs}")
    
    # Verificar se são iguais
    if media_root_abs == temp_folder_abs:
        print("✅ MEDIA_ROOT e TEMP_UPLOAD_FOLDER estão alinhados!")
    else:
        print("❌ MEDIA_ROOT e TEMP_UPLOAD_FOLDER estão diferentes!")
        return False
    
    # Testar default_storage.path()
    test_filename = "test_file.txt"
    storage_path = default_storage.path(test_filename)
    expected_path = os.path.join(settings.TEMP_UPLOAD_FOLDER, test_filename)
    
    print(f"default_storage.path('{test_filename}'): {storage_path}")
    print(f"Caminho esperado: {expected_path}")
    
    if storage_path == expected_path:
        print("✅ default_storage.path() está funcionando corretamente!")
    else:
        print("❌ default_storage.path() não está retornando o caminho esperado!")
        return False
    
    # Verificar se o diretório existe
    if os.path.exists(settings.TEMP_UPLOAD_FOLDER):
        print(f"✅ Diretório {settings.TEMP_UPLOAD_FOLDER} existe!")
    else:
        print(f"❌ Diretório {settings.TEMP_UPLOAD_FOLDER} não existe!")
        print("Criando diretório...")
        os.makedirs(settings.TEMP_UPLOAD_FOLDER, exist_ok=True)
        print("✅ Diretório criado!")
    
    return True

def test_file_creation():
    """Testa a criação de arquivo usando default_storage"""
    print("\n=== Teste de Criação de Arquivo ===")
    
    test_filename = "test_storage_fix.txt"
    test_content = b"Teste de storage fix"
    
    try:
        # Simular o que o código faz
        file_path = default_storage.path(test_filename)
        print(f"Tentando criar arquivo em: {file_path}")
        
        with open(file_path, 'wb') as f:
            f.write(test_content)
        
        print("✅ Arquivo criado com sucesso!")
        
        # Verificar se o arquivo existe
        if os.path.exists(file_path):
            print("✅ Arquivo existe no local esperado!")
            
            # Limpar arquivo de teste
            os.remove(file_path)
            print("✅ Arquivo de teste removido!")
            return True
        else:
            print("❌ Arquivo não foi encontrado no local esperado!")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao criar arquivo: {e}")
        return False

if __name__ == "__main__":
    print("Testando correção do problema de storage...\n")
    
    config_ok = test_storage_configuration()
    file_ok = test_file_creation()
    
    print("\n=== Resultado Final ===")
    if config_ok and file_ok:
        print("✅ Todos os testes passaram! O problema foi corrigido.")
    else:
        print("❌ Alguns testes falharam. Verifique a configuração.")
