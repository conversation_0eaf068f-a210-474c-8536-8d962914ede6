# =============================================================================
# Multi-stage Dockerfile for Kontent Application
# =============================================================================

# Build stage - for compiling dependencies
FROM python:3.9-slim-bullseye AS builder

# Build arguments
ARG arg_revision
ARG arg_build_date

# Labels for better maintainability
LABEL maintainer="Keeps Team"
LABEL version="1.0"
LABEL description="Kontent Application - Content Management System"
LABEL revision=${arg_revision}
LABEL build_date=${arg_build_date}

# Set environment variables for build
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    g++ \
    gcc \
    libpq-dev \
    swig \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements.txt /tmp/
RUN pip install --upgrade pip setuptools wheel && \
    pip install gunicorn && \
    pip install -r /tmp/requirements.txt && \
    pip install python-dateutil

# Download NLTK data
RUN python -c "import nltk; nltk.download('punkt', download_dir='/opt/nltk_data')"

# =============================================================================
# Production stage - minimal runtime image
FROM python:3.9-slim-bullseye AS production

# Build arguments
ARG arg_revision
ARG arg_build_date

# Labels for better maintainability
LABEL maintainer="Keeps Team"
LABEL version="1.0"
LABEL description="Kontent Application - Content Management System"
LABEL revision=${arg_revision}
LABEL build_date=${arg_build_date}

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/opt/venv/bin:$PATH" \
    NLTK_DATA="/opt/nltk_data" \
    REVISION=${arg_revision:-unknown} \
    BUILD_DATE=${arg_build_date:-unknown}

# Install only runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    ffmpeg \
    libpq5 \
    supervisor \
    netcat-openbsd \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd -r kontent && useradd -r -g kontent -d /app -s /bin/bash kontent

# Set working directory
WORKDIR /app

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv
COPY --from=builder /opt/nltk_data /opt/nltk_data

# Copy application code
COPY --chown=kontent:kontent . /app/
COPY --chown=kontent:kontent supervisord.conf /etc/supervisord.conf

# Make entrypoint executable
RUN chmod +x /app/entrypoint.sh

# Create necessary directories with proper permissions
RUN mkdir -p /app/static /app/media /app/temp && \
    chown -R kontent:kontent /app/static /app/media /app/temp

# Switch to non-root user
USER kontent

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health-check', timeout=10)" || exit 1

# Define the entry point
ENTRYPOINT ["/app/entrypoint.sh"]
