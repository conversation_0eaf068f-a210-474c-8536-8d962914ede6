"""
Django settings for config project.
"""

import os
from distutils import util

from colorlog import ColoredFormatter
from corsheaders.defaults import default_headers
from django.utils.log import DEFAULT_LOGGING

BASE_DIR = os.path.dirname(os.path.dirname(__file__))
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")

SECRET_KEY = "5i%ul!b#rui^zipl!+u5nh0+7@j4wtu6o=!0_99t3axph$@oc="

_test = os.getenv("TEST", "False")
TEST = util.strtobool(_test)
DEBUG = os.getenv("DEBUG", "False") == "True"
ALLOWED_HOSTS = ["*"]
APPEND_SLASH = False

INSTALLED_APPS = [
    "django.contrib.staticfiles",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "corsheaders",
    "drf_yasg",
    "rest_framework",
    "django_injector",
    "learn_content.apps.LearnContentConfig",
    "assessment_database.apps.AssessmentDatabaseConfig",
]
if ENVIRONMENT in [
    "stage",
    "staging",
    "production",
]:  # or True to enable local APM monitoring
    INSTALLED_APPS.append("elasticapm.contrib.django")

SCHEDULER_CONFIG = {
    "apscheduler.jobstores.default": {
        "class": "django_apscheduler.jobstores:DjangoJobStore"
    },
    "apscheduler.executors.processpool": {"type": "threadpool"},
}

SCHEDULER_AUTOSTART = True
SCHEDULER_TRANSCRIBE_PROCESS_INTERVAL = os.getenv(
    "SCHEDULER_TRANSCRIBE_PROCESS_INTERVAL", 10
)
SCHEDULER_PDF_PROCESS_INTERVAL = os.getenv("SCHEDULER_TRANSCRIBE_PROCESS_INTERVAL", 1)
SCHEDULER_LINK_AUDIO_VIDEO_PROCESS = os.getenv("SCHEDULER_LINK_AUDIO_VIDEO_PROCESS", 5)
SCHEDULER_GDRIVE_FILE_PROCESS = os.getenv("SCHEDULER_LINK_AUDIO_VIDEO_PROCESS", 4)
SCHEDULER_CLEAN_ELASTIC_SEARCH_INTERVAL = os.getenv(
    "SCHEDULER_CLEAN_ELASTIC_SEARCH_INTERVAL", 20000
)
LIMIT_VIDEO_DURATION_TO_PROCESS = os.getenv("LIMIT_VIDEO_DURATION_TO_PROCESS", 1200)
LIMIT_COMPREHEND_WORDS = os.getenv("", 8000)

PDF_TYPE = os.getenv("PDF_TYPE", "application/pdf")

# Key to PDFTRON used for compress pdf files https://www.pdftron.com/pws/get-key/
PDFTRON_KEY = os.getenv("PDFTRON_KEY")

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "custom.disable_options_method_middleware.DisableOptionsMethodMiddleware",
]

ROOT_URLCONF = "config.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": ["templates/"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "config.wsgi.application"

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ.get("DATABASE_NAME", "kontent_tst"),
        "USER": os.environ.get("DATABASE_USER", "postgres"),
        "PASSWORD": os.environ.get("DATABASE_PASSWORD", "postgres"),
        "HOST": os.environ.get("DATABASE_HOST", "localhost"),
        "PORT": os.environ.get("DATABASE_PORT", "5432"),
    }
}

# Internationalization
LANGUAGE_CODE = "en-us"
TIME_ZONE = "UTC"
USE_I18N = True
USE_L10N = True
USE_TZ = False

STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")
MEDIA_ROOT = "temp"
TEMP_UPLOAD_FOLDER = "{}/temp".format(BASE_DIR)

REST_FRAMEWORK = {
    "DEFAULT_RENDERER_CLASSES": ("rest_framework.renderers.JSONRenderer",),
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "authentication.keeps_authentication.KeepsAuthentication"
    ],
    "EXCEPTION_HANDLER": "custom.custom_exception_handler.custom_exception_handler",
    "DEFAULT_PAGINATION_CLASS": "config.pagination_config.StandardResultsSetPagination",
    "PAGE_SIZE": 100,
}

SWAGGER_SETTINGS = {
    "USE_SESSION_AUTH": False,
    "SECURITY_DEFINITIONS": {},
    "VALIDATOR_URL": "",
    "OPERATIONS_SORTER": "method",
    "TAGS_SORTER": None,
    "DOC_EXPANSION": "list",
    "DEEP_LINKING": False,
    "SHOW_EXTENSIONS": True,
    "DEFAULT_AUTO_SCHEMA_CLASS": "config.docs.SwaggerAutoSchemaCustom",
    "DEFAULT_INFO": "config.docs.api_info",
    "DEFAULT_MODEL_RENDERING": "model",
    "DEFAULT_MODEL_DEPTH": 2,
}

ELASTICSEARCH_HOST = os.getenv("ELASTICSEARCH_HOST")
ELASTICSEARCH_USER = os.getenv("ELASTICSEARCH_USER")
ELASTICSEARCH_PASS = os.getenv("ELASTICSEARCH_PASS")
ELASTICSEARCH_INDEX = os.getenv("ELASTICSEARCH_INDEX", "index-test")

gcp_credentials_path = os.path.join(
    BASE_DIR, "config/google_credentials/credential.json"
)
GOOGLE_APPLICATION_CREDENTIALS = os.getenv(
    "GOOGLE_APPLICATION_CREDENTIALS", gcp_credentials_path
)
GOOGLE_STORAGE_BUCKET = os.getenv("GOOGLE_STORAGE_BUCKET", "kontent-transcript")
GOOGLE_CLOUD_PLATFORM_PROJECT_ID = os.getenv(
    "GOOGLE_CLOUD_PLATFORM_PROJECT_ID", "keeps-learn-platform"
)

CLOUD_CONVERT_KEY = os.getenv("CLOUD_CONVERT_KEY")

# AWS Configuration - Consolidated credentials for all services
AWS_STREAMING_URL = os.getenv(
    "AWS_STREAMING_URL", "https://contents-stage.keepsdev.com"
)
AWS_BUCKET_NAME = os.getenv("AWS_BUCKET_NAME", "keeps.kontent.media.hml")
AWS_BASE_S3_URL = os.getenv("AWS_BASE_S3_URL", "https://s3.amazonaws.com")
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
AWS_REGION_NAME = os.getenv("AWS_REGION_NAME", "us-east-1")

# Transcribe Service Configuration
TRANSCRIBE_SERVICE = os.getenv("TRANSCRIBE_SERVICE", "amazon")  # Alternative : google
TRANSCRIBE_WORDS_PER_MINUTE = os.getenv("TRANSCRIBE_WORDS_PER_MINUTE", 500)
AWS_TRANSCRIBE_THRESHOLD = os.getenv("AWS_TRANSCRIBE_THRESHOLD")
AWS_TRANSCRIBE_BUCKET = os.getenv("AWS_TRANSCRIBE_BUCKET")
AWS_TRANSCRIBE_VOCABULARY = os.getenv("AWS_TRANSCRIBE_VOCABULARY")

# AWS Services - Using consolidated credentials and region
# All AWS services (S3, Comprehend, Rekognition, Transcribe) will use the same credentials and region

CORS_ALLOW_HEADERS = default_headers + ("x-client", "traceparent", "tracestate")

CORS_ALLOW_METHODS = (
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
)

CORS_ORIGIN_ALLOW_ALL = True
CORS_ORIGIN_WHITELIST = (
    "http://localhost:4200",
    "http://127.0.0.1:4200",
    "http://localhost:4100",
    "http://127.0.0.1:4100",
    "http://kizup-api.keepsdev.com",
    "http://konquest-api.keepsdev.com",
    "http://myaccount-api.keepsdev.com",
    "http://kontent-api.keepsdev.com",
    "http://kizup.keepsdev.com",
    "http://konquest.keepsdev.com",
    "http://myaccount.keepsdev.com",
    "http://account.keepsdev.com",
    "http://kontent.keepsdev.com",
    "http://kizup-api-stage.keepsdev.com",
    "http://konquest-api-stage.keepsdev.com",
    "http://myaccount-api-stage.keepsdev.com",
    "http://kontent-api-stage.keepsdev.com",
    "http://kizup-stage.keepsdev.com",
    "http://konquest-stage.keepsdev.com",
    "http://myaccount-stage.keepsdev.com" "http://kontent-stage.keepsdev.com",
    "https://kizup-api.keepsdev.com",
    "https://konquest-api.keepsdev.com",
    "https://myaccount-api.keepsdev.com",
    "https://kontent-api.keepsdev.com",
    "https://kizup.keepsdev.com",
    "https://konquest.keepsdev.com",
    "https://myaccount.keepsdev.com",
    "https://account.keepsdev.com",
    "https://kontent.keepsdev.com",
    "https://kizup-api-stage.keepsdev.com",
    "https://konquest-api-stage.keepsdev.com",
    "https://myaccount-api-stage.keepsdev.com",
    "https://kontent-api-stage.keepsdev.com",
    "https://kizup-stage.keepsdev.com",
    "https://konquest-stage.keepsdev.com",
    "https://myaccount-stage.keepsdev.com",
    "https://learning-platform-api-stage.keepsdev.com",
)

BROKER_URL = os.getenv("BROKER_URL", "amqp://guest:guest@localhost:5672")
CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL", "amqp://guest:guest@localhost:5672")
CELERY_RESULT_BACKEND = os.getenv(
    "CELERY_RESULT_BACKEND", "amqp://guest:guest@localhost:5672"
)
CELERY_ACCEPT_CONTENT = ["application/json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = "America/Sao_Paulo"
CELERY_QUEUE = os.getenv("CELERY_QUEUE", "kontent-stage")
KONQUEST_CELERY_QUEUE = os.getenv("KONQUEST_CELERY_QUEUE", "konquest-stage")
CELERYD_PREFETCH_MULTIPLIER = 2
CELERY_ACKS_LATE = True
CELERY_AMQP_TASK_RESULT_EXPIRES = 1000
CELERY_INTERVAL_START = os.environ.get("CELERY_INTERVAL_START", 0)
CELERY_INTERVAL_STEP = os.environ.get("CELERY_INTERVAL_STEP", 2)
CELERY_INTERVAL_MAX = os.environ.get("CELERY_INTERVAL_MAX", 20)
CELERY_MAX_RETRIES = os.environ.get("CELERY_INTERVAL_START", 10)

# Whether to store the task return values or not (tombstones).
# If you still want to store errors, just not successful return values, you can set task_store_errors_even_if_ignored.
CELERY_IGNORE_RESULT = bool(util.strtobool(os.getenv("CELERY_IGNORE_RESULT", "True")))

SYNC_PLATFORM_ENABLED = True
EXCHANGE_INTEGRATIONS = os.getenv("EXCHANGE_INTEGRATIONS", "integrations")

KEYCLOAK_FORMAT_PUBLIC_KEY = """-----BEGIN PUBLIC KEY-----
{}
-----END PUBLIC KEY-----"""
KEYCLOAK_SERVER_URL = os.environ.get("KEYCLOAK_SERVER_URL")
KEYCLOAK_REALM = os.environ.get("KEYCLOAK_REALM")
KEYCLOAK_CLIENT_ID = os.environ.get("KEYCLOAK_CLIENT_ID")
KEYCLOAK_CLIENT_SECRET_KEY = os.environ.get("KEYCLOAK_CLIENT_SECRET_KEY")
KEYCLOAK_CLIENT_PUBLIC_KEY = os.environ.get("KEYCLOAK_CLIENT_PUBLIC_KEY")
KEYCLOAK_CONFIG = {
    "KEYCLOAK_SERVER_URL": KEYCLOAK_SERVER_URL,
    "KEYCLOAK_REALM": KEYCLOAK_REALM,
    "KEYCLOAK_CLIENT_ID": KEYCLOAK_CLIENT_ID,
    "KEYCLOAK_CLIENT_SECRET_KEY": KEYCLOAK_CLIENT_SECRET_KEY,
    "KEYCLOAK_CLIENT_PUBLIC_KEY": KEYCLOAK_FORMAT_PUBLIC_KEY.format(
        KEYCLOAK_CLIENT_PUBLIC_KEY
    ),
}

KEEPS_SECRET_TOKEN_INTEGRATION = os.getenv("KEEPS_SECRET_TOKEN_INTEGRATION")
KONQUEST_API_URL = os.getenv(
    "KONQUEST_API_URL", "https://learning-platform-api-stage.keepsdev.com/konquest/"
)
KONQUEST_WEB_URL = os.getenv("KONQUEST_WEB_URL", "https://konquest-stage.keepsdev.com/")

LOGGING = DEFAULT_LOGGING

DISCORD_WEBHOOK = os.getenv(
    "DISCORD_WEBHOOK",
    "https://discord.com/api/webhooks/1078667375651594280/LzOVrnLShL1Piq6hXyao9Rgvr5JiZDUVWvCcuJ4Dqe8Tpk6ilMJCWNL6hKzhj70HbuaR",
)

# Configurando o handler do console para níveis INFO e DEBUG
LOGGING["handlers"]["console"] = {
    "level": "DEBUG" if DEBUG else "WARNING",
    "class": "logging.StreamHandler",
    "formatter": "verbose_console",
}

# Adicionando o handler do Discord
LOGGING["handlers"]["discord"] = {
    "level": "ERROR",  # Enviar apenas logs de nível ERROR ou superior
    "filters": ["require_debug_false"],  # Apenas quando o DEBUG estiver False
    "class": "custom.discord_webhook_v2.DiscordWebhookLoggerV2",
    "webhook_url": DISCORD_WEBHOOK,
    "formatter": "verbose",
}

# Formatadores de log para tornar as mensagens mais legíveis
LOGGING["formatters"] = {
    "verbose_console": {
        "()": ColoredFormatter,
        "format": (
            "%(log_color)s%(levelname)-3s [%(asctime)s] [%(name)s] [%(module)s]: %(message)s%(reset)s"
        ),
        "datefmt": "%Y-%m-%d %H:%M:%S",
        "log_colors": {
            "DEBUG": "cyan",
            "INFO": "green",
            "WARNING": "yellow",
            "ERROR": "red",
            "CRITICAL": "bold_red",
        },
    },
    "verbose": {
        "format": "{levelname} {asctime} {module} {message}",
        "style": "{",
    },
    "simple": {
        "format": "{levelname} {message}",
        "style": "{",
    },
    "django.server": DEFAULT_LOGGING["formatters"]["django.server"],
}

# Configurando os loggers
LOGGING["loggers"]["django"] = {
    "handlers": ["console"],
    "level": "INFO" if DEBUG else "ERROR",
    "propagate": True,
}

LOGGING["loggers"]["kontent_log"] = {
    "handlers": ["console", "discord"],
    "level": "DEBUG" if DEBUG else "WARNING",
    "propagate": True,
}

# https://www.elastic.co/guide/en/apm/agent/python/master/django-support.html#django-logging
LOGGING["loggers"]["elasticapm.errors"] = {
    "level": "ERROR",
    "handlers": ["console"],
    "propagate": False,
}
LOGGING["loggers"]["apm"] = {
    "level": "WARNING",
    "handlers": ["elasticapm"],
    "propagate": False,
}
LOGGING["handlers"]["elasticapm"] = {
    "level": "WARNING",
    "class": "elasticapm.contrib.django.handlers.LoggingHandler",
}

ELASTIC_APM = {
    "SERVICE_NAME": os.getenv("ELASTIC_APM_SERVICE_NAME", "kontent"),
    "SERVER_URL": os.getenv(
        "ELASTIC_APM_SERVER_URL", "https://keeps.apm.us-east-1.aws.cloud.es.io"
    ),
    "SECRET_TOKEN": os.getenv("ELASTIC_APM_SECRET_TOKEN"),
    "ENVIRONMENT": os.getenv("ELASTIC_APM_ENVIRONMENT", "development"),
    # "DEBUG": True,  # enable local APM monitoring
    # Fine-tuning - Ref.: https://www.elastic.co/guide/en/apm/agent/python/current/configuration.html
    "TRANSACTION_SAMPLE_RATE": 0.1,  # default: 1.0
    "SPAN_STACK_TRACE_MIN_DURATION": -1,  # default: 5ms
    "SPAN_COMPRESSION_SAME_KIND_MAX_DURATION": "5ms",  # default: 0ms,
}

try:
    MAX_PROCESS_PDF_PAGES = int(os.getenv("MAX_PROCESS_PDF_PAGES", 30))
except ValueError as error:
    raise ValueError(f"MAX_PROCESS_PDF_PAGES is not a valid integer: {error}")


CLEANER_QUERIES_DIRECTORY = os.getenv("QUERIES_DIRECTORY", "cleaner/queries")

# only active this for tests
SUSPEND_SIGNALS = bool(util.strtobool(os.getenv("SUSPEND_SIGNALS", "False")))

TIMEOUT_PROCESS_XLSX_FILE = int(os.getenv("TIMEOUT_PROCESS_XLSX_FILE", 1))

PLAYER_VIMEO_URL = os.getenv("PLAYER_VIMEO_URL", "https://player.vimeo.com/video/{}")
REFERER_VIMEO_EMBEDDED = os.getenv(
    "REFERER_VIMEO_EMBEDDED", "https://konquest.keepsdev.com/"
)
VIMEO_TOKEN = os.getenv("VIMEO_TOKEN", "")
GOOGLE_YOUTUBE_API_KEY = os.getenv("GOOGLE_YOUTUBE_API_KEY", "")


# Video Compressor Settings (Smartzap PRESET)
VIDEO_COMPRESSOR_SETTINGS = {
    "MAX_WIDTH": int(os.getenv("VIDEO_COMPRESSOR_MAX_WIDTH", 1280)),
    "MAX_HEIGHT": int(os.getenv("VIDEO_COMPRESSOR_MAX_HEIGHT", 720)),
    "VIDEO_BITRATE": os.getenv("VIDEO_COMPRESSOR_VIDEO_BITRATE", "2000k"),
    "AUDIO_BITRATE": os.getenv("VIDEO_COMPRESSOR_AUDIO_BITRATE", "96k"),
    "CRF": int(os.getenv("VIDEO_COMPRESSOR_CRF", 32)),
    "PRESET": os.getenv("VIDEO_COMPRESSOR_PRESET", "slow"),
    "MINIMUM_FILE_SIZE": int(os.getenv("VIDEO_MINIMUM_FILE_SIZE_IN_MB", 16)),
    "MAXIMUM_DURATION": int(os.getenv("VIDEO_MAXIMUM_DURATION_IN_SECOND", 300)),
}
