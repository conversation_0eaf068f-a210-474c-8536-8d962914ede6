#!/bin/bash
set -e

# Function to log with timestamp
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Function to wait for database
wait_for_db() {
    if [ -n "$DATABASE_HOST" ] && [ -n "$DATABASE_PORT" ]; then
        log "Waiting for database at $DATABASE_HOST:$DATABASE_PORT..."

        while ! nc -z "$DATABASE_HOST" "$DATABASE_PORT"; do
            log "Database not ready, waiting..."
            sleep 2
        done

        log "Database is ready!"
    else
        log "Database connection variables not set, skipping database wait"
    fi
}

# Function to run Django setup
setup_django() {
    log "Running Django migrations..."
    python manage.py migrate --noinput

    log "Collecting static files..."
    python manage.py collectstatic --noinput --clear

    if [ -f "analyze/downloads.py" ]; then
        log "Running analyze downloads..."
        python analyze/downloads.py
    fi
}

# Function to create necessary directories
create_directories() {
    mkdir -p /app/static /app/media /app/temp
    log "Created necessary directories"
}

# Main execution
main() {
    log "Starting Kontent application..."

    # Create directories
    create_directories

    # Wait for database
    wait_for_db

    # Setup Django
    setup_django

    log "Setup completed, starting application..."

    # Execute the main command
    exec "$@"
}

# Run main function
main
