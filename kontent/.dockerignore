# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/
myenv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/
.reports/
coverage.xml
*.cover
.hypothesis/

# Git
.git/
.gitignore

# CI/CD
.github/
.gitlab-ci.yml

# Documentation
docs/
*.md
README*

# Development tools
postman/
node_modules/
.dockerignore
Dockerfile*
docker-compose*

# Static files (will be collected during build)
static/
staticfiles/

# Media files (should be handled by external storage)
media/

# Temporary files
temp/
tmp/
*.tmp
*.log

# OS
.DS_Store
Thumbs.db

# Database
*.db
*.sqlite3

# Backup files
*.bak
*.backup
