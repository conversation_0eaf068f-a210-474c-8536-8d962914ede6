[supervisord]
Description=Kontent Services
nodaemon=true
loglevel=info
user=kontent
logfile=/dev/stdout
logfile_maxbytes=0
pidfile=/tmp/supervisord.pid

[unix_http_server]
file=/tmp/supervisor.sock
chmod=0700
chown=kontent:kontent

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:celery]
directory=/app
command=celery -A tasks worker -P threads --loglevel=INFO -n worker1@%%h --concurrency=2
user=kontent
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
redirect_stderr=false
autostart=true
autorestart=true
startsecs=10
stopwaitsecs=600
killasgroup=true
priority=999
environment=PYTHONPATH="/app"

[program:scheduler]
directory=/app
command=python cron_schedulers.py
user=kontent
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
redirect_stderr=false
autostart=true
autorestart=true
startsecs=10
stopwaitsecs=600
priority=1000
environment=PYTHONPATH="/app"
