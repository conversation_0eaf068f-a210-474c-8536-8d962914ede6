# pylint: disable=cyclic-import
import json
import os
from math import ceil

import subprocess
from urllib.parse import urlparse
import mutagen
from django_injector import inject

from pdfminer.pdfdocument import PDFDocument
from pdfminer.pdfparser import PDFParser
from pdfminer.pdftypes import resolve1

from config import settings
from config.settings import LIMIT_VIDEO_DURATION_TO_PROCESS
from di import Container
from custom.keeps_exception_handler import KeepsBadRequestError
from analyze import AnalyzeContent
from analyze.parses import pdf, ms_office, html_files
from utils.extract_file_info import get_file_size
from utils.pdf_compressor import compress
from custom import DISCORD_WEBHOOK
import logging
from PIL import Image



# pylint: disable=too-many-locals
# pylint: disable=too-many-instance-attributes
# pylint: disable=no-value-for-parameter
class AnalyzeVideoAudio(AnalyzeContent):
    @inject
    def __init__(self,
                 container: Container,
                 file_name: str,
                 points_rules: int,
                 points_quantity: int,
                 file_path: str,
                 workspace_id: str = None
                 ) -> None:
        super().__init__()
        self._file_to_analyze = file_path
        self._uploader = container.aws_s3_client()
        self._file_name = file_name
        self._points_rules = points_rules
        self._points_quantity = points_quantity
        self._transcribe_client = container.aws_transcribe_client
        self._workspace_id = workspace_id

    @staticmethod
    def get_audio_duration(file_path:str):
        ffprobe_command = [
            'ffprobe', 
            '-v', 'quiet', 
            '-print_format', 'json', 
            '-show_format', 
            '-show_streams', 
            file_path
        ]
        
        result = subprocess.run(ffprobe_command, capture_output=True, text=True)
        probe_info = json.loads(result.stdout)
        duration = float(probe_info['format'].get('duration', 0))
        return duration

    def set_file_to_analyze(self, file_to_analyze: str):
        self._file_to_analyze = file_to_analyze

    def execute(self) -> dict:
        """
        :return dict(upload, duration, points, transcribe_job):
        """
        upload = self._uploader.send_file(file_name=self._file_name,
                                          file_path=self._file_to_analyze, workspace_id=self._workspace_id)
        duration = None
        try:
            duration = int(mutagen.File(self._file_to_analyze).info.length)
        except Exception as error:
            DISCORD_WEBHOOK.emit_short_message('Error get vimeo content minutes', error)
            logging.error(str(error))
        if not duration:
            duration = int(self.get_audio_duration(self._file_to_analyze))

        points = round(int(duration / (self._points_quantity * 60)) + 0.6) * self._points_rules
        file_size = get_file_size(self._file_to_analyze)

        analyzed = True
        transcribe_job = None
        if duration < LIMIT_VIDEO_DURATION_TO_PROCESS:
            analyzed = False
            transcribe_job_async = self._transcribe_client.transcribe_async(file_url=upload['url'])
            transcribe_job = transcribe_job_async['TranscriptionJob']['TranscriptionJobName']

        os.remove(self._file_to_analyze)

        return {
            "url": upload.get('url'),
            "duration": duration,
            "points": points,
            "analyzed": analyzed,
            "file_size": file_size,
            "file_mime_type": upload['file_mime_type'],
            "original_file_size": file_size,
            "transcribe_job": transcribe_job
        }

    def transcribe(self, job_name) -> dict:
        try:
            job = self._transcribe_client.get_transcribe_job(job_name=job_name)
            status = job['TranscriptionJob']['TranscriptionJobStatus']
            bucket_file_media = urlparse(job['TranscriptionJob']['Media']['MediaFileUri']).path.split('/')
        except Exception as error:
            self.logger(
                method=f"Transcribe job not found | JOB: {job_name}",
                error=str(error)
            )
            return {"analyzed": True}

        if status == 'COMPLETED':
            transcript_file = job['TranscriptionJob']['Transcript']['TranscriptFileUri']
            bucket_transcript_file = urlparse(transcript_file).path.split('/')
            try:
                transcript_raw = self._transcribe_client.get_transcribe_file(
                    bucket_name=bucket_transcript_file[1], file_name=bucket_transcript_file[2]
                )
                transcripts = [t['transcript'] for t in transcript_raw['results']['transcripts']]
                transcript = ' '.join(transcripts)
                keys_tags, entities, language = self.tag(transcript)
                summarize = self.summary(text=transcript, language=language)

                # remove transcribe file created
                self._uploader.delete_file(
                    bucket=bucket_transcript_file[1],
                    file_name=bucket_transcript_file[2]
                )

                # remove temp file uploaded
                if bucket_file_media[1] == 'keeps.transcribe':
                    self._uploader.delete_file(
                        bucket=bucket_file_media[1],
                        file_name=f'{bucket_file_media[2]}/{bucket_file_media[3]}'
                    )

                return {
                    "transcript": transcript,
                    "tags": keys_tags,
                    "entities": entities,
                    "language": language,
                    "summary": summarize,
                    "analyzed": True
                }

            except Exception as error:
                self.logger(
                    method=f"Async Transcribe | Exception | JOB: {job_name}",
                    error=str(error)
                )
                return {"analyzed": True}

        elif status == 'FAILED':
            self.logger(
                method=f"Async Transcribe | STATUS FAILED | JOB: {job_name}",
                error=job['TranscriptionJob']['FailureReason']
            )
            return {"analyzed": True}
        else:
            return {"analyzed": False}


class AnalyzeMsOffice(AnalyzeContent):
    MINIMUM_PRESENTATION_PAGE_DURATION = 10

    @inject
    def __init__(self,
                 container: Container,
                 file_name: str,
                 points_rules: int,
                 points_quantity: int,
                 file_path: str,
                 workspace_id: str = None
                 ) -> None:
        super().__init__()
        self._google_drive_client = container.google_drive_client
        self._uploader = container.aws_s3_client()
        self._file_to_analyze = file_path
        self._file_name = file_name
        self._points_rules = points_rules
        self._points_quantity = points_quantity
        self._file_size_limit = 100000000
        self._workspace_id = workspace_id

    def execute(self) -> dict:
        """
        :return dict(upload, duration, points, transcript):
        """
        extension = self._file_to_analyze.split('.')[-1:][0]
        if extension not in self._google_drive_client.files_extensions_allowed():
            os.remove(self._file_to_analyze)
            raise KeepsBadRequestError(i18n="gdrive_file_not_allowed", detail="GDrive file not allowed")
        if os.path.getsize(self._file_to_analyze) > self._file_size_limit:
            os.remove(self._file_to_analyze)
            raise KeepsBadRequestError(i18n="gdrive_file_size_exceeded", detail="File size exceeded limit")
        upload = self._uploader.send_file(
            file_name=self._file_name, file_path=self._file_to_analyze, workspace_id=self._workspace_id
        )
        self._object = upload.get('url')
        result = {
            "url": upload.get('url'),
            "analyzed": False
        }

        return result

    def process(self, extension):
        transcript = None
        duration = 120
        points = 20

        if extension == 'docx':
            transcript = ms_office.docx2text(self._file_to_analyze)
            duration = self.text_time_duration(transcript)
            points = self.compute_points_by_text(transcript)

        elif extension == 'pptx':
            transcript = ms_office.pptx2text(self._file_to_analyze)
            count_slides = ms_office.ppt_count_slides(self._file_to_analyze)
            duration = self.text_time_duration(transcript) + (count_slides * self.MINIMUM_PRESENTATION_PAGE_DURATION)
            points = self.compute_points_by_duration(duration)

        elif extension in ['xlsx']:
            transcript = ms_office.xlsx2text(self._file_to_analyze)
            if transcript:
                duration = self.text_time_duration(transcript)
                points = self.compute_points_by_text(transcript)

        elif extension in ['xls']:
            transcript = ms_office.xls2text(self._file_to_analyze)
            duration = self.text_time_duration(transcript)
            points = self.compute_points_by_text(transcript)

        elif extension in ['html']:
            transcript = html_files.html_file2text(self._file_to_analyze)
            duration = self.text_time_duration(transcript)
            points = self.compute_points_by_text(transcript)
        keys_tags, entities, language = self.tag(transcript)
        summarize = self.summary(text=transcript, language=language)

        return {
            "duration": duration,
            "points": points,
            "transcript": transcript,
            "tags": keys_tags,
            "entities": entities,
            "language": language,
            "summary": summarize,
            "analyzed": True
        }


class AnalyzePDF(AnalyzeContent):

    @inject
    def __init__(self,
                 container: Container,
                 file_name: str,
                 points_rules: int,
                 points_quantity: int,
                 file_path: str,
                 workspace_id: str = None) -> None:
        super().__init__()
        self.file_to_analyze = file_path
        self._uploader = container.aws_s3_client()
        self._file_name = file_name
        self._points_rules = points_rules
        self._points_quantity = points_quantity
        self._workspace_id = workspace_id

    def execute(self) -> dict:
        """
        :return dict(upload, duration, points, transcript):
        """
        upload = self._uploader.send_file(
            file_name=self._file_name, file_path=self.file_to_analyze, workspace_id=self._workspace_id
        )
        os.remove(self.file_to_analyze)

        return {
            "url": upload.get('url'),
            "analyzed": False
        }

    def process(self) -> dict:
        count_pages = self.count_pages()
        transcript = pdf.pdf2text(self.file_to_analyze, min([settings.MAX_PROCESS_PDF_PAGES, count_pages]))
        duration = self.text_time_duration(transcript)
        points = max(ceil((self.count_words(transcript) / self._points_quantity) * self._points_rules), 1)
        if count_pages > settings.MAX_PROCESS_PDF_PAGES:
            duration_avg_by_page = (duration / settings.MAX_PROCESS_PDF_PAGES)
            points_avg_by_page = (points / settings.MAX_PROCESS_PDF_PAGES)
            duration = duration_avg_by_page * count_pages
            points = points_avg_by_page * count_pages

        keys_tags, entities, language = self.tag(transcript)
        summarize = self.summary(text=transcript, language=language)

        return {
            "duration": duration,
            "points": points,
            "transcript": transcript,
            "tags": keys_tags,
            "entities": entities,
            "language": language,
            "summary": summarize,
            "analyzed": True
        }

    def compress(self, file_url: str) -> None:
        compress(self.file_to_analyze, self.file_to_analyze)
        bucket, key = self._uploader.extract_s3_url_bucket_key(file_url)
        self._uploader.delete_object(bucket, key)
        self._uploader.send_file(
            file_name=key, file_path=self.file_to_analyze, sub_folder=None, bucket=bucket
        )

    def count_pages(self) -> int:
        with open(self.file_to_analyze, 'rb') as file:
            parser = PDFParser(file)
            document = PDFDocument(parser)
            return resolve1(document.catalog['Pages'])['Count']


class AnalyzeImage(AnalyzeContent):

    @inject
    def __init__(self,
                 container: Container,
                 file_name: str,
                 points_rules: int,
                 points_quantity: int,
                 file_path: str,
                 workspace_id: str = None
                 ) -> None:
        super().__init__()
        self.format_to_convert = ['webp']
        self._file_to_analyze = self.convert_image(file_path.replace('/kontent/', '/'))
        self._uploader = container.aws_s3_client()
        self._file_name = file_name
        self._points_rules = points_rules
        self._points_quantity = points_quantity
        self._transcript_client = container.aws_rekognition
        self._workspace_id = workspace_id
    
    def convert_image(self, file_path):
        if file_path.split('.')[1] not in self.format_to_convert:
            return file_path
        image_convert_jpeg = Image.open(file_path)
        image_convert_jpeg = image_convert_jpeg.convert('RGB')
        image_convert_path = file_path.split('.')[0] + '.jpeg'
        image_convert_jpeg.save(image_convert_path, 'jpeg')
        os.remove(file_path)
        return image_convert_path

    def execute(self) -> dict:
        """
        :return dict(upload, duration, points, transcript):
        """
        upload = self._uploader.send_file(
            file_name=self._file_name, file_path=self._file_to_analyze, workspace_id=self._workspace_id
        )

        process = self.process(url=upload.get('url'))
        os.remove(self._file_to_analyze)
        return process

    def process(self, url):
        transcript = self._transcript_client.detect_image_text(url)
        if len(transcript) == 0:
            duration = 10
            points = 1
        else:
            duration = self.text_time_duration(transcript)
            points = self.compute_points_by_duration(duration)
        keys_tags, entities, language = self.tag(transcript)
        summarize = self.summary(text=transcript, language=language)

        return {
            "url": url,
            "duration": duration,
            "points": points,
            "transcript": transcript,
            "tags": keys_tags,
            "entities": entities,
            "language": language,
            "summary": summarize,
            "analyzed": True
        }
