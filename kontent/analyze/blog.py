# -*- coding: utf-8 -*-
from injector import inject

from di import Container
from analyze import AnalyzeContent
from analyze.parses.blog import url2text


class AnalyzeBlog(AnalyzeContent):
    """
    Analyzer for blog content from URLs
    """

    @inject
    def __init__(self,
                 points_rules: int,
                 points_quantity: int,
                 url: str,
                 container: Container = None,
                 workspace_id: str = None) -> None:
        super().__init__(container or Container(), workspace_id, points_rules, points_quantity)
        self._url = url

    def execute(self) -> dict:
        """
        Extract text from blog URL and analyze it
        :return dict: analysis results with transcript, duration, points, etc.
        """
        transcript = ""
        duration = 60  
        points = 1
        keys_tags = []
        entities = []
        language = "pt"
        summarize = ""

        try:
            transcript = url2text(self._url)
            duration = self.text_time_duration(transcript)
            points = self.compute_points_by_text(transcript)
            try:
                keys_tags, entities, language = self.tag(transcript)
            except Exception as tag_error:
                self.logger("AnalyzeBlog - Tag extraction", str(tag_error))
                keys_tags, entities, language = [], [], "pt"

            try:
                summarize = self.summary(text=transcript, language=language)
            except Exception as summary_error:
                self.logger("AnalyzeBlog - Summary generation", str(summary_error))
                summarize = ""
        except Exception as error:
            self.logger("AnalyzeBlog - URL extraction", str(error))

        return {
            "url": self._url,
            "duration": duration,
            "points": points,
            "transcript": transcript,
            "tags": keys_tags,
            "entities": entities,
            "language": language,
            "summary": summarize,
            "analyzed": True
        }
